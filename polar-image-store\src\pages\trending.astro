---
import Layout from '../layouts/Layout.astro';
import ProductCard from '../components/ProductCard.astro';
import StructuredData from '../components/StructuredData.astro';
import { getTrendingProducts } from '../utils/polar';
import type { LocalProduct } from '../types/polar';

// Fetch trending products (last 7 days, top 12)
let trendingProducts: LocalProduct[] = [];
let error: string | null = null;

try {
  trendingProducts = await getTrendingProducts(7, 12);
} catch (e) {
  console.error('Error fetching trending products:', e);
  error = 'Failed to load trending products';
}

// Breadcrumb data for SEO
const breadcrumbData = {
  items: [
    { name: 'Home', url: import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store' },
    { name: 'Trending', url: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/trending` }
  ]
};
---

<Layout
  title="Trending Products - InfPik"
  description="Discover the most popular digital images and artwork based on recent sales. Updated daily to showcase what\'s trending right now."
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/trending`}
>
  <!-- Breadcrumb Structured Data -->
  <StructuredData type="BreadcrumbList" data={breadcrumbData} />

  <div class="w-full px-4 md:px-8 py-8">
    <section class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-900 mb-4">Trending Now</h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        Our top-selling images from the past week, loved by creators worldwide
      </p>
    </section>

    {error && (
      <div class="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
        <div class="flex items-center gap-3 text-red-800">
          <svg class="w-6 h-6 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
          <div>
            <p class="font-semibold">⚠️ {error}</p>
            <p class="text-sm">Please try again later.</p>
          </div>
        </div>
      </div>
    )}

    {!error && trendingProducts.length === 0 && (
      <div class="text-center py-16">
        <p class="text-lg text-gray-600">No trending products available right now. Check back soon!</p>
      </div>
    )}

    {!error && trendingProducts.length > 0 && (
      <div class="columns-2 sm:columns-3 lg:columns-4 gap-6">
        {trendingProducts.slice(0, 16).map((product) => (
          <ProductCard product={product} />
        ))}
      </div>
    )}
  </div>
</Layout>