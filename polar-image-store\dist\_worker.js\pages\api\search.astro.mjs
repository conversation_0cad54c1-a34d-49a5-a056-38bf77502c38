globalThis.process??={},globalThis.process.env??={};import{c as createPolarClient,t as transformPolarProduct,e as extractUniqueTags,g as getTagDisplayName}from"../../chunks/polar_ZJjBaMUg.mjs";export{renderers}from"../../renderers.mjs";const prerender=!1,GET=async({url:e,locals:t})=>{try{const a=e.searchParams.get("q"),r=t?.runtime?.env,o=createPolarClient(r),s=r?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca",n=await o.products.list({organizationId:s,isArchived:!1}),l=(n.result?.items||[]).map(transformPolarProduct).filter((e=>null!==e)),c=extractUniqueTags(l);if(!a||a.trim().length<2){const e=c.map((e=>{const t=l.filter((t=>t.tags&&t.tags.includes(e)));return{id:e,name:getTagDisplayName(e),displayName:getTagDisplayName(e),count:t.length,url:`/products/tag/${encodeURIComponent(e)}`}})).sort(((e,t)=>t.count-e.count)).slice(0,10);return new Response(JSON.stringify({results:[],popularTags:e}),{status:200,headers:{"Content-Type":"application/json","Cache-Control":"public, max-age=300"}})}const i=a.toLowerCase().trim(),p=c.filter((e=>{const t=getTagDisplayName(e).toLowerCase();return e.toLowerCase().includes(i)||t.includes(i)})).map((e=>{const t=l.filter((t=>t.tags&&t.tags.includes(e)));return{id:e,name:getTagDisplayName(e),displayName:getTagDisplayName(e),count:t.length,url:`/products/tag/${encodeURIComponent(e)}`}}));return p.sort(((e,t)=>{const a=e.name.toLowerCase()===i||e.id.toLowerCase()===i,r=t.name.toLowerCase()===i||t.id.toLowerCase()===i;return a&&!r?-1:!a&&r?1:t.count!==e.count?t.count-e.count:e.name.localeCompare(t.name)})),new Response(JSON.stringify({results:p,total:p.length,query:a}),{status:200,headers:{"Content-Type":"application/json","Cache-Control":"public, max-age=60"}})}catch(e){return console.error("Search API error:",e),new Response(JSON.stringify({error:"Search failed"}),{status:500,headers:{"Content-Type":"application/json"}})}},_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};