/**
 * Test script for Cloudflare Image Transform integration
 * Run with: node scripts/test-image-optimization.js
 */

import { getOptimizedImageUrl, getResponsiveImageUrls, ImagePresets, getOptimalFormat } from '../src/utils/imageOptimization.js';

// Test image URLs (example Polar.sh S3 URLs)
const testImages = [
  'https://polar-sh-uploads.s3.amazonaws.com/example-image-1.jpg',
  'https://polar-sh-uploads.s3.amazonaws.com/example-image-2.png',
  'https://some-bucket.s3.amazonaws.com/test-image.webp'
];

console.log('🖼️  Testing Cloudflare Image Transform Integration\n');

// Test 1: Basic optimization
console.log('1️⃣  Testing basic image optimization:');
testImages.forEach((url, index) => {
  const optimized = getOptimizedImageUrl(url, {
    width: 800,
    height: 600,
    quality: 85,
    format: 'auto'
  });
  console.log(`   Image ${index + 1}: ${optimized}`);
});

console.log('\n2️⃣  Testing image presets:');
testImages.forEach((url, index) => {
  console.log(`   Image ${index + 1}:`);
  console.log(`     Product Card: ${ImagePresets.productCard(url)}`);
  console.log(`     Thumbnail: ${ImagePresets.thumbnail(url)}`);
  console.log(`     Product Detail: ${ImagePresets.productDetail(url)}`);
});

console.log('\n3️⃣  Testing responsive images:');
const responsiveTest = getResponsiveImageUrls(testImages[0], {
  sizes: [320, 640, 960, 1280],
  densities: [1, 2],
  quality: 85,
  format: 'auto'
});
console.log(`   Main src: ${responsiveTest.src}`);
console.log(`   Srcset: ${responsiveTest.srcset}`);

console.log('\n4️⃣  Testing format detection:');
const userAgents = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/20100101 Firefox/93.0'
];

userAgents.forEach((ua, index) => {
  const format = getOptimalFormat(ua);
  const browser = index === 0 ? 'Chrome' : index === 1 ? 'Safari' : 'Firefox';
  console.log(`   ${browser}: ${format}`);
});

console.log('\n5️⃣  Testing local images (should remain unchanged):');
const localImages = ['/placeholder-image.svg', '/logo.svg'];
localImages.forEach(url => {
  const result = getOptimizedImageUrl(url);
  console.log(`   ${url} → ${result}`);
});

console.log('\n✅ Image optimization test completed!');
console.log('\n📝 Next steps:');
console.log('   1. Deploy to Cloudflare Pages');
console.log('   2. Enable Image Resizing in Cloudflare dashboard');
console.log('   3. Test with real Polar.sh image URLs');
console.log('   4. Monitor performance improvements');

// Generate example URLs for testing
console.log('\n🔗 Example optimized URLs to test:');
const exampleUrl = 'https://polar-sh-uploads.s3.amazonaws.com/example.jpg';
console.log(`   AVIF: ${getOptimizedImageUrl(exampleUrl, { format: 'avif', width: 800, quality: 85 })}`);
console.log(`   WebP: ${getOptimizedImageUrl(exampleUrl, { format: 'webp', width: 800, quality: 85 })}`);
console.log(`   Auto: ${getOptimizedImageUrl(exampleUrl, { format: 'auto', width: 800, quality: 85 })}`);
