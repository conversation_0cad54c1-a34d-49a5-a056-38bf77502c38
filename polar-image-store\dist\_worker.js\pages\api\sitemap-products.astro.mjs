globalThis.process??={},globalThis.process.env??={};import{c as createPolarClient}from"../../chunks/polar_ZJjBaMUg.mjs";export{renderers}from"../../renderers.mjs";const GET=async({locals:e})=>{try{const t=e?.runtime?.env,r=createPolarClient(t),a=t?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca",o=await r.products.list({organizationId:a,limit:100}),n=o.result?.items||[],s=[],p=new Set,c=new Set;n.forEach((e=>{if(e.name){const t=e.name.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"");s.push(`https://infpik.store/products/${t}`)}e.metadata&&Object.entries(e.metadata).forEach((([e,t])=>{e.startsWith("category:")&&"string"==typeof t&&p.add(t),e.startsWith("tag:")&&"string"==typeof t&&c.add(t)}))}));const i=Array.from(p).map((e=>`https://infpik.store/products/category/${encodeURIComponent(e)}`)),l=Array.from(c).map((e=>`https://infpik.store/products/tag/${encodeURIComponent(e)}`)),m=`<?xml version="1.0" encoding="UTF-8"?>\n<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n${[...s,...i,...l].map((e=>`  <url>\n    <loc>${e}</loc>\n    <lastmod>${(new Date).toISOString().split("T")[0]}</lastmod>\n    <changefreq>weekly</changefreq>\n    <priority>0.8</priority>\n  </url>`)).join("\n")}\n</urlset>`;return new Response(m,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600"}})}catch(e){return console.error("Error generating products sitemap:",e),new Response("Error generating sitemap",{status:500})}},prerender=!1,_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};