---
import Layout from '../layouts/Layout.astro';

// Get checkout ID from URL params if available
const checkoutId = Astro.url.searchParams.get('checkout_id');
---

<Layout title="Purchase Successful - InfPik" description="Thank you for your purchase!">
  <section class="flex justify-center items-center min-h-[60vh] py-8">
    <div class="text-center max-w-2xl mx-4 p-8 bg-white rounded-2xl shadow-xl border border-gray-100">
      <div class="text-6xl mb-6">
        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
      </div>

      <h1 class="text-4xl font-bold text-green-600 mb-4">Purchase Successful!</h1>
      <p class="text-xl text-gray-600 mb-8 leading-relaxed">
        Thank you for your purchase. Your digital images are now available for download.
      </p>

      {checkoutId && (
        <div class="bg-green-50 border-l-4 border-green-500 p-4 rounded-lg mb-8">
          <p class="text-green-800">
            <span class="font-semibold">Order ID:</span>
            <span class="font-mono text-sm">{checkoutId}</span>
          </p>
        </div>
      )}

      <div class="text-left mb-8">
        <h2 class="text-2xl font-semibold text-gray-900 mb-6 text-center">What's Next?</h2>
        <ul class="space-y-4">
          <li class="flex items-start gap-3">
            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Check your email for download links and receipt</span>
          </li>
          <li class="flex items-start gap-3">
            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Download your high-resolution images</span>
          </li>
          <li class="flex items-start gap-3">
            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Review the license terms for usage rights</span>
          </li>
        </ul>
      </div>

      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a
          href="/products"
          class="inline-flex items-center justify-center gap-2 px-6 py-3 border-2 border-primary-600 text-primary-600 rounded-full font-semibold transition-all duration-200 hover:bg-primary-600 hover:text-white hover:-translate-y-0.5"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
          Browse More Images
        </a>
        <a
          href="/"
          class="inline-flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-primary-600 to-purple-600 text-white rounded-full font-semibold transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
          </svg>
          Back to Home
        </a>
      </div>
    </div>
  </section>
</Layout>


