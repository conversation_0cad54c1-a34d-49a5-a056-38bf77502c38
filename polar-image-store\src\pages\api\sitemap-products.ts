import type { APIRoute } from 'astro';
import { createPolarClient } from '../../utils/polar';

export const GET: APIRoute = async ({ locals }) => {
  try {
    // Get runtime environment from Cloudflare context
    const env = locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || import.meta.env.POLAR_ORGANIZATION_ID;

    if (!organizationId) {
      return new Response('Organization ID not configured', { status: 500 });
    }

    // Fetch all products
    const response = await polar.products.list({
      organizationId,
      limit: 100, // Adjust as needed
    });

    const products = response.result?.items || [];
    
    // Generate URLs for products
    const productUrls: string[] = [];
    const categories = new Set<string>();
    const tags = new Set<string>();

    products.forEach(product => {
      // Add individual product URL
      if (product.name) {
        const slug = product.name.toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '');
        productUrls.push(`https://infpik.store/products/${slug}`);
      }

      // Collect categories and tags from metadata
      if (product.metadata) {
        Object.entries(product.metadata).forEach(([key, value]) => {
          if (key.startsWith('category:') && typeof value === 'string') {
            categories.add(value);
          }
          if (key.startsWith('tag:') && typeof value === 'string') {
            tags.add(value);
          }
        });
      }
    });

    // Generate category URLs
    const categoryUrls = Array.from(categories).map(category => 
      `https://infpik.store/products/category/${encodeURIComponent(category)}`
    );

    // Generate tag URLs
    const tagUrls = Array.from(tags).map(tag => 
      `https://infpik.store/products/tag/${encodeURIComponent(tag)}`
    );

    // Combine all URLs
    const allUrls = [...productUrls, ...categoryUrls, ...tagUrls];

    // Generate XML sitemap
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allUrls.map(url => `  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`).join('\n')}
</urlset>`;

    return new Response(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('Error generating products sitemap:', error);
    return new Response('Error generating sitemap', { status: 500 });
  }
};

export const prerender = false;
