globalThis.process??={},globalThis.process.env??={};export{renderers}from"../renderers.mjs";const GET=async()=>{const e=`<?xml version="1.0" encoding="UTF-8"?>\n<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n  <sitemap>\n    <loc>https://infpik.store/sitemap-0.xml</loc>\n    <lastmod>${(new Date).toISOString().split("T")[0]}</lastmod>\n  </sitemap>\n  <sitemap>\n    <loc>https://infpik.store/api/sitemap-products</loc>\n    <lastmod>${(new Date).toISOString().split("T")[0]}</lastmod>\n  </sitemap>\n</sitemapindex>`;return new Response(e,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600"}})},prerender=!1,_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};