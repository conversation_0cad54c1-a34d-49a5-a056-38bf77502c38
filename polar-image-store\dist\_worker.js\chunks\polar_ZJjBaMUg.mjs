globalThis.process??={},globalThis.process.env??={};import{P as Polar}from"./sdk_DEQ9AU5A.mjs";function createPolarClient(t){const e=t?.POLAR_ACCESS_TOKEN||"polar_oat_igptjJXuxXOyeTQmpqxDK5QHFnnU0JNNPS1Tc3eHD7u";if(!e)throw new Error("POLAR_ACCESS_TOKEN is required");return new Polar({accessToken:e,server:"production"})}function transformPolarProduct(t){if(!t||!t.id||!t.name)return console.warn("Invalid polar product:",t),null;const e=t.prices?.[0],a=e?.priceAmount||0,r=e?.priceCurrency||"USD",n=t.metadata?.category?t.metadata.category.trim():null,o=[...t.metadata?.tags?t.metadata.tags.split(",").map((t=>t.trim())):[],...extractTags(t.description||"")].filter(Boolean);return{id:t.id,name:t.name,description:t.description||"",price:a/100,currency:r,images:t.medias?.map((t=>t.publicUrl))||[],slug:generateSlug(t.name),isAvailable:!t.isArchived,tags:o,category:n,createdAt:t.createdAt,updatedAt:t.modifiedAt||t.createdAt}}function generateSlug(t){return t&&"string"==typeof t?t.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,""):""}function extractTags(t){const e=t.match(/#(\w+)/g);return e?e.map((t=>t.slice(1))):[]}function formatPrice(t,e="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:e.toUpperCase()}).format(t)}function getProductsByCategory(t,e){return"all"===e?t:t.filter((t=>t.category===e))}function getCategoryDisplayName(t){return t.split("-").map((t=>t.charAt(0).toUpperCase()+t.slice(1))).join(" ")}function generateCategoriesWithCounts(t){const e=new Map;t.forEach((t=>{if(t.category){const a=e.get(t.category)||0;e.set(t.category,a+1)}}));const a=Array.from(e.entries()).map((([t,e])=>({id:t,name:getCategoryDisplayName(t),count:e})));return a.sort(((t,e)=>t.name.localeCompare(e.name))),a.unshift({id:"all",name:"All",count:t.length}),a}function extractUniqueTags(t){return t.flatMap((t=>t.tags||[])).filter(Boolean).filter(((t,e,a)=>a.indexOf(t)===e)).sort()}function getProductsByTag(t,e){return"all"===e?t:t.filter((t=>t.tags&&t.tags.includes(e)))}function getTagDisplayName(t){return t.split("-").map((t=>t.charAt(0).toUpperCase()+t.slice(1))).join(" ")}function generateTagsWithCounts(t){const e=new Map;t.forEach((t=>{t.tags&&t.tags.forEach((t=>{const a=e.get(t)||0;e.set(t,a+1)}))}));const a=Array.from(e.entries()).map((([t,e])=>({id:t,name:getTagDisplayName(t),count:e})));return a.sort(((t,e)=>e.count!==t.count?e.count-t.count:t.name.localeCompare(e.name))),a.unshift({id:"all",name:"All Tags",count:t.length}),a}async function getTrendingProducts(t=7,e=10){const a=createPolarClient(),r=new Date,n=new Date(r.getTime()-24*t*60*60*1e3);let o=[];try{o=(await a.orders.list({paid:!0,from:n.toISOString(),to:r.toISOString(),limit:100})).items??[]}catch(t){return console.error("Failed to fetch orders for trending products",t),[]}const s=new Map;o.forEach((t=>{(t.products||[]).forEach((t=>{const e=t.id||t.product_id;e&&s.set(e,(s.get(e)||0)+1)}))}));const c=[...s.entries()].sort(((t,e)=>e[1]-t[1])).slice(0,e).map((([t])=>t)),i=[];return await Promise.all(c.map((async t=>{try{const e=transformPolarProduct(await a.products.get(t));e&&i.push(e)}catch(e){console.warn(`Unable to fetch product ${t}:`,e)}}))),i}export{getProductsByCategory as a,getCategoryDisplayName as b,createPolarClient as c,getProductsByTag as d,extractUniqueTags as e,getTrendingProducts as f,getTagDisplayName as g,formatPrice as h,generateCategoriesWithCounts as i,generateTagsWithCounts as j,transformPolarProduct as t};